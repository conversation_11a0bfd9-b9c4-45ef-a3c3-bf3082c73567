#!/usr/bin/env swift

import Foundation
import UserNotifications

class NotificationTester: NSObject, UNUserNotificationCenterDelegate {
    let center = UNUserNotificationCenter.current()
    
    override init() {
        super.init()
        center.delegate = self
    }
    
    func requestPermission() {
        print("🔔 请求通知权限...")
        
        center.requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ 权限请求失败: \(error)")
                } else {
                    print("✅ 权限请求结果: \(granted ? "已授权" : "被拒绝")")
                    if granted {
                        self.sendTestNotification()
                    }
                }
            }
        }
    }
    
    func sendTestNotification() {
        print("📤 发送测试通知...")
        
        let content = UNMutableNotificationContent()
        content.title = "ZenTomato 通知测试"
        content.body = "这是一个测试通知，用于验证通知系统是否正常工作"
        content.sound = .default
        
        let request = UNNotificationRequest(
            identifier: "test-notification",
            content: content,
            trigger: nil
        )
        
        center.add(request) { error in
            if let error = error {
                print("❌ 发送失败: \(error)")
            } else {
                print("✅ 通知发送成功")
            }
        }
    }
    
    // MARK: - UNUserNotificationCenterDelegate
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        print("📱 通知即将显示: \(notification.request.content.title)")
        completionHandler([.banner, .sound, .badge])
    }
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        print("👆 用户点击了通知: \(response.notification.request.content.title)")
        completionHandler()
    }
}

// 运行测试
let tester = NotificationTester()
tester.requestPermission()

// 保持程序运行
RunLoop.main.run()
