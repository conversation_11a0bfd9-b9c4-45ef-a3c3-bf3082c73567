# 禅番茄 ZenTomato - 产品需求文档（PRD）

**文档版本**：v1.0.0  
**更新日期**：2025年1月13日  
**产品负责人**：水木易  
**目标平台**：macOS 11.0+

---

## 1. 产品概述

### 1.1 产品定位
禅番茄（ZenTomato）是一款专为macOS设计的番茄钟应用，将传统番茄工作法与禅意美学完美融合，帮助用户专注工作、平衡生活。产品定位为高端精品工具类应用，强调设计美感和用户体验。

### 1.2 核心价值主张
- **禅意美学**：通过优雅的视觉设计营造宁静专注的工作氛围
- **原生体验**：深度集成macOS系统特性，提供流畅自然的交互
- **专注本质**：回归番茄工作法核心，去除冗余功能
- **无缝工作流**：菜单栏常驻，快捷键控制，最小化干扰

### 1.3 目标用户
- **主要用户群体**：
  - 知识工作者（程序员、设计师、作家等）
  - 学生和研究人员
  - 自由职业者和远程工作者
  - 追求高效工作方法的专业人士

- **用户特征**：
  - 使用macOS系统
  - 注重工作效率和时间管理
  - 追求优质的软件体验
  - 偏好简洁优雅的设计风格

### 1.4 产品目标
- **短期目标**（3个月）：
  - Mac App Store成功上架
  - 获得1000+活跃用户
  - 用户评分达到4.5+星

- **中期目标**（6个月）：
  - 月活跃用户达到5000+
  - 建立用户社区
  - 推出专业版功能

- **长期目标**（12个月）：
  - 成为Mac平台番茄钟应用Top 5
  - 国际化覆盖5种主要语言
  - 跨平台扩展（iOS/iPadOS）

---

## 2. 功能需求

### 2.1 核心功能模块

#### 2.1.1 智能计时系统
**功能描述**：提供灵活可配置的番茄钟计时功能

**详细需求**：
- **时间设置**
  - 工作时间：1-60分钟可调，默认25分钟
  - 短休息时间：1-60分钟可调，默认5分钟
  - 长休息时间：1-60分钟可调，默认15分钟
  - 支持长按快速调节数值

- **周期管理**
  - 工作周期数：1-10次可配置，默认4次
  - 完成指定周期后自动切换到长休息
  - 支持手动跳过当前阶段

- **计时控制**
  - 一键开始/停止
  - 支持暂停和继续
  - 自动切换工作/休息状态
  - 可选休息后自动停止

**技术实现**：
- 使用DispatchSourceTimer实现精确计时
- SwiftUI + Combine响应式状态管理
- UserDefaults持久化用户配置

#### 2.1.2 沉浸式音效系统
**功能描述**：提供多层次的音效反馈，增强专注体验

**详细需求**：
- **音效类型**
  - 开始音效（windup）：工作开始时的激励音
  - 结束音效（ding）：阶段结束时的提醒音
  - 背景音效（ticking）：可选的滴答声，营造专注氛围

- **音量控制**
  - 每种音效独立音量调节（0-2倍）
  - 支持快速静音/恢复
  - 双击滑块重置为默认音量

- **播放逻辑**
  - 工作开始：播放开始音效 + 启动背景音
  - 工作结束：停止背景音 + 播放结束音效
  - 休息期间：不播放背景音

**技术实现**：
- AVFoundation框架处理音频
- 高品质WAV格式音频文件
- 音量渐变效果（0.1秒淡入淡出）

#### 2.1.3 智能通知系统
**功能描述**：与macOS通知中心深度集成，提供及时提醒

**详细需求**：
- **通知类型**
  - 休息开始通知：包含休息时长信息
  - 休息结束通知：提醒继续工作
  - 支持交互式操作（跳过休息）

- **权限管理**
  - 首次使用时请求通知权限
  - 提供系统设置快捷入口
  - 优雅处理权限被拒绝的情况

- **通知行为**
  - 应用在前台时仍显示通知
  - 支持通知中心历史记录
  - 可通过通知快速操作

**技术实现**：
- UserNotifications框架
- UNNotificationCategory定义交互类别
- 后台处理通知响应

#### 2.1.4 菜单栏集成
**功能描述**：常驻菜单栏，提供快速访问和状态显示

**详细需求**：
- **状态显示**
  - 菜单栏图标（番茄图标）
  - 可选显示倒计时（MM:SS格式）
  - 工作/休息状态视觉区分

- **弹出面板**
  - 点击图标显示控制面板
  - 面板尺寸：380x680像素
  - 自动定位避免超出屏幕
  - 点击外部自动关闭

- **快速操作**
  - 主控制按钮（开始/停止）
  - 标签页切换（时长/设置/声音）
  - 底部快捷操作栏

**技术实现**：
- NSStatusItem创建菜单栏项
- 自定义NSWindow替代NSPopover
- 圆角半透明背景设计

### 2.2 系统设置功能

#### 2.2.1 全局快捷键
**功能描述**：支持自定义全局快捷键控制

**详细需求**：
- 默认快捷键：无（用户自定义）
- 支持组合键设置
- 快捷键录制器UI组件
- 冲突检测和提示

**技术实现**：
- KeyboardShortcuts框架
- 系统级热键注册

#### 2.2.2 开机自启动
**功能描述**：可选的系统启动时自动运行

**详细需求**：
- 设置开关控制
- 使用系统登录项API
- 静默启动到菜单栏

**技术实现**：
- LaunchAtLogin框架
- SMLoginItemSetEnabled API

#### 2.2.3 URL Scheme支持
**功能描述**：支持外部应用控制

**详细需求**：
- 协议：zentomato://
- 支持命令：
  - startstop：切换计时状态
  - start：开始计时
  - stop：停止计时

**技术实现**：
- Info.plist注册URL Scheme
- NSAppleEventManager处理事件

### 2.3 界面设计需求

#### 2.3.1 设计语言
**视觉风格**：禅意极简主义

**配色方案**：
- 主红色 `#CC3333`：专注工作的活力色
- 柔绿色 `#66B366`：休息放松的舒缓色
- 静蓝色 `#6699CC`：深度专注的平静色
- 温金色 `#E6B34D`：任务完成的温暖色
- 禅灰色 `#F2F2F2`：简洁优雅的背景色
- 文字灰 `#4D4D4D`：主要文字颜色

**设计原则**：
- 大量留白，营造呼吸感
- 圆角矩形元素（16px圆角）
- 柔和阴影效果（8px模糊）
- 优雅的动画过渡（0.2-0.8秒）

#### 2.3.2 界面布局

**主控面板**（380x680px）：
```
┌─────────────────────────┐
│     呼吸动画背景          │ 80px
│     [计时按钮]           │
├─────────────────────────┤
│  [时长] [设置] [声音]     │ 60px
├─────────────────────────┤
│                         │
│     内容区域             │ 350px
│   （标签页内容）          │
│                         │
├─────────────────────────┤
│  关于 | 主界面 | 退出      │ 60px
└─────────────────────────┘
```

**欢迎界面**（400x600px）：
- 顶部：应用图标 + 品牌信息
- 中部：特色功能卡片（4个）
- 底部：主操作按钮 + 版权信息

**关于界面**（400x380px）：
- 应用图标和名称
- 版本信息
- 版权声明
- 隐私政策/服务协议链接

#### 2.3.3 交互设计

**动画效果**：
- 按钮悬停：缩放1.05倍
- 按钮点击：缩放0.95倍
- 呼吸动画：2秒循环，1.0-1.2倍缩放
- 界面切换：淡入淡出0.3秒

**手势支持**：
- 长按：快速增减数值
- 双击：重置为默认值
- 拖动：调节滑块数值

**反馈机制**：
- 视觉反馈：颜色变化、动画效果
- 音频反馈：操作音效
- 触觉反馈：（预留，未来版本）

### 2.4 本地化需求

#### 2.4.1 当前支持语言
- 简体中文（zh-Hans）：主要语言
- 英文（en）：国际版本

#### 2.4.2 本地化内容
- 界面文字
- 通知内容
- 音效（语言相关）
- 日期时间格式

#### 2.4.3 本地化文件
- Localizable.strings：界面文字
- InfoPlist.strings：系统信息

---

## 3. 非功能需求

### 3.1 性能需求
- **启动时间**：< 1秒
- **内存占用**：< 50MB
- **CPU占用**：空闲时 < 1%，活动时 < 5%
- **响应时间**：UI操作 < 100ms

### 3.2 兼容性需求
- **系统版本**：macOS 11.0 (Big Sur) 及以上
- **处理器**：Apple Silicon (M1/M2/M3) + Intel
- **分辨率**：支持Retina显示屏
- **语言**：简体中文、英文

### 3.3 安全性需求
- **数据安全**：所有数据本地存储
- **隐私保护**：不收集用户个人信息
- **权限最小化**：仅请求必要系统权限
- **代码签名**：Apple开发者证书签名

### 3.4 可用性需求
- **易用性**：零学习成本，即装即用
- **可访问性**：支持VoiceOver
- **错误处理**：优雅的错误提示
- **帮助文档**：内置使用指南

### 3.5 可维护性需求
- **代码规范**：Swift官方编码规范
- **模块化设计**：MVVM架构
- **日志系统**：JSON格式日志
- **版本管理**：语义化版本号

---

## 4. 技术架构

### 4.1 技术栈
- **开发语言**：Swift 5.9
- **UI框架**：SwiftUI
- **响应式框架**：Combine
- **音频处理**：AVFoundation
- **通知系统**：UserNotifications
- **数据存储**：UserDefaults
- **开发工具**：Xcode 15.0+

### 4.2 项目结构
```
ZenTomato/
├── App.swift              # 应用入口和生命周期
├── View.swift             # UI组件和视图
├── Timer.swift            # 计时器核心逻辑
├── State.swift            # 状态管理
├── Player.swift           # 音频播放
├── Notifications.swift    # 通知管理
├── Log.swift              # 日志系统
└── Assets.xcassets/       # 资源文件
    ├── AppIcon           # 应用图标
    ├── BarIcon           # 菜单栏图标
    └── Sounds/           # 音效文件
```

### 4.3 架构模式
- **MVVM**：Model-View-ViewModel
- **响应式编程**：Combine + @Published
- **依赖注入**：@EnvironmentObject
- **单例模式**：共享实例管理

### 4.4 第三方依赖
- **LaunchAtLogin**：开机自启动
- **KeyboardShortcuts**：全局快捷键

---

## 5. 发布计划

### 5.1 版本规划

#### v1.0.0 - 基础版本（当前）
- ✅ 核心番茄钟功能
- ✅ 音效系统
- ✅ 通知系统
- ✅ 菜单栏集成
- ✅ 基础设置

#### v1.1.0 - 增强版本（计划中）
- ⏳ 统计功能（日/周/月统计）
- ⏳ 任务标签
- ⏳ iCloud同步
- ⏳ 深色模式优化

#### v1.2.0 - 专业版本（未来）
- ⏳ 白噪音库扩展
- ⏳ 专注时长排行
- ⏳ 团队协作功能
- ⏳ Apple Watch应用

### 5.2 发布渠道
- **主要渠道**：Mac App Store
- **备选渠道**：官网直接下载
- **定价策略**：
  - 基础版：免费
  - 专业版：¥30（计划中）

### 5.3 营销策略
- **产品定位**：精品工具应用
- **目标用户**：效率工具爱好者
- **推广渠道**：
  - Product Hunt
  - 少数派
  - V2EX社区
  - 独立开发者社区

---

## 6. 测试需求

### 6.1 功能测试
- **计时准确性**：误差 < 0.1秒
- **状态转换**：所有状态正确切换
- **音效播放**：各场景正确播放
- **通知发送**：准时且内容正确

### 6.2 兼容性测试
- **系统版本**：macOS 11-14全覆盖
- **处理器架构**：M1/M2/M3 + Intel
- **多显示器**：正确定位弹窗
- **系统语言**：中英文切换

### 6.3 性能测试
- **长时间运行**：24小时稳定性
- **内存泄漏**：无内存泄漏
- **电池消耗**：低功耗运行
- **响应速度**：流畅无卡顿

### 6.4 用户体验测试
- **新手引导**：5分钟上手
- **核心流程**：3次点击完成
- **错误恢复**：优雅处理异常
- **视觉一致性**：符合设计规范

---

## 7. 风险评估

### 7.1 技术风险
- **风险**：macOS系统API变更
- **概率**：中
- **影响**：高
- **应对**：持续关注WWDC，及时适配

### 7.2 市场风险
- **风险**：同类产品竞争激烈
- **概率**：高
- **影响**：中
- **应对**：差异化设计，精品路线

### 7.3 运营风险
- **风险**：App Store审核被拒
- **概率**：低
- **影响**：高
- **应对**：严格遵循审核指南

---

## 8. 成功指标

### 8.1 产品指标
- **下载量**：首月1000+
- **日活跃用户**：30%
- **用户留存**：7日留存40%
- **应用评分**：4.5星以上

### 8.2 技术指标
- **崩溃率**：< 0.1%
- **启动成功率**：> 99.9%
- **响应时间**：P95 < 100ms
- **错误率**：< 0.5%

### 8.3 用户反馈
- **满意度**：> 90%
- **推荐意愿**：NPS > 50
- **功能请求**：月度收集整理
- **问题反馈**：24小时响应

---

## 9. 附录

### 9.1 竞品分析
| 产品 | 优势 | 劣势 | 我们的机会 |
|-----|-----|-----|----------|
| Be Focused | 功能全面 | 界面复杂 | 简化体验 |
| Toggl Track | 强大统计 | 订阅制 | 买断制 |
| Forest | 游戏化 | 过度娱乐 | 专注本质 |
| Pomotodo | 任务管理 | 臃肿 | 轻量化 |

### 9.2 用户故事
1. 作为程序员，我希望在编码时保持专注，避免频繁打断
2. 作为设计师，我需要优雅的界面，让工作环境更舒适
3. 作为学生，我想要简单易用的工具，帮助我管理学习时间
4. 作为自由职业者，我需要灵活的时间设置，适应不同项目

### 9.3 设计素材
- **图标规格**：16x16 到 1024x1024
- **音效格式**：WAV 44.1kHz 16bit
- **字体选择**：SF Pro Display/Text
- **动画时长**：0.2-0.8秒标准

### 9.4 相关文档
- [Human Interface Guidelines](https://developer.apple.com/design/)
- [App Store Review Guidelines](https://developer.apple.com/app-store/review/guidelines/)
- [SwiftUI Documentation](https://developer.apple.com/documentation/swiftui/)
- [番茄工作法](https://zh.wikipedia.org/wiki/番茄工作法)

---

## 10. 文档变更记录

| 版本 | 日期 | 作者 | 变更内容 |
|-----|------|-----|---------|
| v1.0.0 | 2025-01-13 | 水木易 | 初始版本发布 |

---

**文档说明**：
本产品需求文档（PRD）为禅番茄（ZenTomato）项目的核心指导文件，详细定义了产品的功能需求、设计规范、技术架构和发布计划。文档将随产品迭代持续更新，确保与实际开发保持同步。

**联系方式**：
- 产品负责人：水木易
- 邮箱：<EMAIL>
- GitHub：https://github.com/shuimuyicom

---

*本文档遵循敏捷开发原则，鼓励团队成员提出改进建议，共同打造优秀的产品体验。*