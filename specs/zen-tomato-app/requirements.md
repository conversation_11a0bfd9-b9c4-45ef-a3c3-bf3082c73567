# 需求文档

## 介绍

禅番茄（ZenTomato）是一款专为macOS设计的番茄钟应用，将传统番茄工作法与禅意美学完美融合。应用采用原生SwiftUI开发，提供菜单栏常驻、智能计时、沉浸式音效和优雅通知等核心功能，帮助用户专注工作、平衡生活。

## 需求

### 需求 1 - 智能计时系统

**用户故事：** 作为一名知识工作者，我希望能够使用可配置的番茄钟计时功能，以便根据我的工作习惯调整时间设置并保持专注。

#### 验收标准

1. 当用户设置工作时间时，系统应当允许1-60分钟的调整范围，默认值为25分钟
2. 当用户设置短休息时间时，系统应当允许1-60分钟的调整范围，默认值为5分钟
3. 当用户设置长休息时间时，系统应当允许1-60分钟的调整范围，默认值为15分钟
4. 当用户配置工作周期数时，系统应当允许1-10次的设置范围，默认值为4次
5. 当完成指定工作周期后，系统应当自动切换到长休息状态
6. 当用户点击开始按钮时，系统应当开始倒计时并显示剩余时间
7. 当用户点击停止按钮时，系统应当停止计时并重置状态
8. 当用户点击暂停按钮时，系统应当暂停计时并保持当前状态
9. 当计时结束时，系统应当自动切换到下一个阶段（工作/休息）
10. 当用户长按时间调节按钮时，系统应当快速增减数值

### 需求 2 - 沉浸式音效系统

**用户故事：** 作为一名用户，我希望通过多层次的音效反馈来增强专注体验，以便在不同的工作阶段获得适当的听觉提示。

#### 验收标准

1. 当工作阶段开始时，系统应当播放开始音效（windup）
2. 当任何阶段结束时，系统应当播放结束音效（ding）
3. 当工作阶段进行中时，系统应当可选播放背景滴答声（ticking）
4. 当用户调节音效音量时，系统应当支持0-2倍的音量范围调节
5. 当用户双击音量滑块时，系统应当重置为默认音量
6. 当用户启用静音时，系统应当停止所有音效播放
7. 当休息阶段时，系统应当停止背景滴答声播放
8. 当音效播放时，系统应当使用0.1秒的淡入淡出效果

### 需求 3 - 智能通知系统

**用户故事：** 作为一名用户，我希望收到及时的系统通知提醒，以便在应用不在前台时也能知道工作和休息状态的变化。

#### 验收标准

1. 当休息阶段开始时，系统应当发送包含休息时长信息的通知
2. 当休息阶段结束时，系统应当发送提醒继续工作的通知
3. 当应用首次使用时，系统应当请求通知权限
4. 如果通知权限被拒绝，系统应当提供系统设置的快捷入口
5. 当应用在前台时，系统应当仍然显示通知
6. 当用户点击通知时，系统应当支持快速操作（如跳过休息）
7. 当通知发送后，系统应当在通知中心保留历史记录

### 需求 4 - 菜单栏集成

**用户故事：** 作为一名用户，我希望应用能够常驻菜单栏并提供快速访问，以便在不干扰当前工作的情况下控制番茄钟。

#### 验收标准

1. 当应用启动时，系统应当在菜单栏显示番茄图标
2. 当计时进行中时，系统应当可选在菜单栏显示倒计时（MM:SS格式）
3. 当处于工作状态时，系统应当通过视觉效果区分工作和休息状态
4. 当用户点击菜单栏图标时，系统应当显示380x680像素的控制面板
5. 当控制面板显示时，系统应当自动定位避免超出屏幕边界
6. 当用户点击面板外部时，系统应当自动关闭控制面板
7. 当面板打开时，系统应当显示主控制按钮和标签页切换
8. 当面板底部时，系统应当提供快捷操作栏（关于、主界面、退出）

### 需求 5 - 系统设置功能

**用户故事：** 作为一名用户，我希望能够配置全局快捷键、开机自启动等系统级功能，以便更好地集成到我的工作流程中。

#### 验收标准

1. 当用户设置全局快捷键时，系统应当支持组合键的录制和设置
2. 当用户设置快捷键时，系统应当检测并提示快捷键冲突
3. 当用户启用开机自启动时，系统应当在系统启动时自动运行应用
4. 当开机自启动时，系统应当静默启动到菜单栏而不显示主界面
5. 当外部应用调用URL Scheme时，系统应当支持zentomato://协议
6. 当收到startstop命令时，系统应当切换当前计时状态
7. 当收到start命令时，系统应当开始计时
8. 当收到stop命令时，系统应当停止计时

### 需求 6 - 用户界面设计

**用户故事：** 作为一名用户，我希望应用具有优雅的禅意美学设计，以便在使用过程中获得愉悦的视觉体验和平静的工作氛围。

#### 验收标准

1. 当应用显示时，系统应当使用禅意极简主义的设计语言
2. 当显示不同状态时，系统应当使用对应的主题色彩（工作红色#CC3333、休息绿色#66B366等）
3. 当界面元素显示时，系统应当使用16px圆角和8px模糊阴影效果
4. 当用户交互时，系统应当提供0.2-0.8秒的优雅动画过渡
5. 当按钮悬停时，系统应当缩放至1.05倍
6. 当按钮点击时，系统应当缩放至0.95倍
7. 当主界面显示时，系统应当包含呼吸动画背景（2秒循环，1.0-1.2倍缩放）
8. 当用户长按数值调节时，系统应当支持快速增减
9. 当用户双击滑块时，系统应当重置为默认值

### 需求 7 - 本地化支持

**用户故事：** 作为一名中文用户，我希望应用能够提供完整的中文界面和本地化体验，以便更自然地使用应用功能。

#### 验收标准

1. 当系统语言为简体中文时，系统应当显示中文界面文字
2. 当系统语言为英文时，系统应当显示英文界面文字
3. 当发送通知时，系统应当使用对应语言的通知内容
4. 当显示时间格式时，系统应当根据系统语言使用相应的日期时间格式
5. 当应用信息显示时，系统应当使用本地化的应用名称和描述

### 需求 8 - 性能和兼容性

**用户故事：** 作为一名macOS用户，我希望应用能够在我的系统上稳定高效地运行，以便获得流畅的使用体验。

#### 验收标准

1. 当应用启动时，系统应当在1秒内完成启动
2. 当应用运行时，系统应当保持内存占用低于50MB
3. 当应用空闲时，系统应当保持CPU占用低于1%
4. 当应用活动时，系统应当保持CPU占用低于5%
5. 当用户操作UI时，系统应当在100ms内响应
6. 当运行在macOS 11.0及以上版本时，系统应当正常工作
7. 当运行在Apple Silicon和Intel处理器时，系统应当正常工作
8. 当使用Retina显示屏时，系统应当提供高清显示效果