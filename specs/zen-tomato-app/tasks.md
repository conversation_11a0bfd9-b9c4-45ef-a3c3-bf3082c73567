# 实现计划

- [ ] 1. 建立项目基础架构和核心数据模型
  - 创建MVVM架构的基础文件结构
  - 实现TimerState、TimerPhase枚举和TimerConfiguration、AudioSettings数据模型
  - 建立颜色系统和动画扩展
  - 配置项目依赖（KeyboardShortcuts、LaunchAtLogin）
  - _需求: 1.1, 1.2, 1.3, 6.1, 6.2, 6.3_

- [ ] 2. 实现核心计时引擎
  - [ ] 2.1 创建TimerEngine类的基础结构
    - 实现ObservableObject协议和@Published属性
    - 创建计时器配置属性（工作时间、休息时间、周期数）
    - 实现基础的开始、停止、暂停方法
    - _需求: 1.1, 1.6, 1.7, 1.8_

  - [ ] 2.2 实现精确计时逻辑
    - 使用DispatchSourceTimer实现精确计时功能
    - 实现倒计时更新和时间格式化
    - 添加计时器状态管理和验证
    - 编写计时精度测试（误差<0.1秒）
    - _需求: 1.6, 1.9, 8.5_

  - [ ] 2.3 实现阶段转换和周期管理
    - 实现工作/休息阶段的自动切换逻辑
    - 添加周期计数和长休息触发机制
    - 实现跳过当前阶段功能
    - 创建状态转换的单元测试
    - _需求: 1.4, 1.5, 1.9, 1.10_

- [ ] 3. 创建音频播放系统
  - [ ] 3.1 实现AudioPlayer基础类
    - 创建AudioPlayer类和音效类型枚举
    - 实现AVFoundation音频播放基础功能
    - 添加音量控制和静音功能
    - 创建音效资源加载机制
    - _需求: 2.1, 2.4, 2.6_

  - [ ] 3.2 实现多种音效播放
    - 添加开始音效（windup）播放功能
    - 添加结束音效（ding）播放功能
    - 实现背景滴答声（ticking）循环播放
    - 实现音效的淡入淡出效果（0.1秒）
    - _需求: 2.1, 2.2, 2.3, 2.8_

  - [ ] 3.3 实现音量控制和用户设置
    - 创建独立的音量控制（0-2倍范围）
    - 实现双击重置默认音量功能
    - 添加音效设置的持久化存储
    - 实现休息期间停止背景音的逻辑
    - _需求: 2.4, 2.5, 2.7_

- [ ] 4. 建立通知系统
  - [ ] 4.1 实现NotificationManager基础功能
    - 创建NotificationManager类和通知权限管理
    - 实现通知权限请求和状态监控
    - 添加权限被拒绝时的引导界面
    - 创建通知内容的本地化支持
    - _需求: 3.3, 3.4, 7.3_

  - [ ] 4.2 实现阶段转换通知
    - 添加休息开始通知（包含时长信息）
    - 添加休息结束通知（提醒继续工作）
    - 实现应用前台时仍显示通知
    - 创建通知历史记录功能
    - _需求: 3.1, 3.2, 3.5, 3.7_

  - [ ] 4.3 实现交互式通知功能
    - 创建通知操作类别（跳过休息）
    - 实现通知响应处理逻辑
    - 添加通知点击后的应用激活
    - 测试交互式通知的用户体验
    - _需求: 3.6_

- [ ] 5. 创建菜单栏集成
  - [ ] 5.1 实现MenuBarManager基础功能
    - 创建MenuBarManager类和NSStatusItem管理
    - 实现菜单栏番茄图标显示
    - 添加菜单栏状态更新机制
    - 实现工作/休息状态的视觉区分
    - _需求: 4.1, 4.3_

  - [ ] 5.2 实现弹出面板系统
    - 创建380x680像素的自定义弹出窗口
    - 实现智能定位避免超出屏幕边界
    - 添加点击外部自动关闭功能
    - 实现面板显示/隐藏的动画效果
    - _需求: 4.4, 4.5, 4.6_

  - [ ] 5.3 添加菜单栏时间显示功能
    - 实现可选的倒计时显示（MM:SS格式）
    - 添加时间显示的开关设置
    - 优化菜单栏文字显示效果
    - 测试不同屏幕分辨率下的显示效果
    - _需求: 4.2, 4.7, 4.8_

- [ ] 6. 实现主用户界面
  - [ ] 6.1 创建主控制面板结构
    - 实现MainView的基础布局（380x680像素）
    - 创建呼吸动画背景组件（2秒循环，1.0-1.2倍缩放）
    - 添加标签页切换功能（时长/设置/声音）
    - 实现底部快捷操作栏（关于、主界面、退出）
    - _需求: 4.7, 4.8, 6.7_

  - [ ] 6.2 实现计时器显示界面
    - 创建TimerView组件和大型时间显示
    - 实现阶段指示器和状态显示
    - 添加主控制按钮（开始/停止/暂停）
    - 创建时间设置控件（1-60分钟可调）
    - _需求: 1.1, 1.2, 1.3, 1.6, 1.7, 1.8_

  - [ ] 6.3 实现设置界面
    - 创建SettingsView组件
    - 添加工作周期数设置（1-10次可配置）
    - 实现自动开始选项设置
    - 创建重置为默认设置功能
    - _需求: 1.4, 1.5_

- [ ] 7. 实现音效设置界面
  - [ ] 7.1 创建音效控制界面
    - 实现AudioView组件和音效开关
    - 创建VolumeSliderView自定义组件
    - 添加各种音效的独立音量滑块
    - 实现音效测试按钮功能
    - _需求: 2.4, 2.5_

  - [ ] 7.2 实现高级音效控制
    - 添加双击滑块重置功能
    - 实现音量百分比显示
    - 创建音效预览播放功能
    - 添加音效设置的实时保存
    - _需求: 2.5_

- [ ] 8. 实现系统集成功能
  - [ ] 8.1 添加全局快捷键支持
    - 集成KeyboardShortcuts库
    - 创建快捷键录制器UI组件
    - 实现快捷键冲突检测和提示
    - 添加快捷键的计时器控制功能
    - _需求: 5.1, 5.2_

  - [ ] 8.2 实现开机自启动功能
    - 集成LaunchAtLogin库
    - 创建开机自启动设置开关
    - 实现静默启动到菜单栏
    - 测试系统登录项的正确注册
    - _需求: 5.3_

  - [ ] 8.3 添加URL Scheme支持
    - 在Info.plist中注册zentomato://协议
    - 实现URL Scheme处理逻辑
    - 添加startstop、start、stop命令支持
    - 创建外部应用控制的测试用例
    - _需求: 5.4, 5.5, 5.6, 5.7_

- [ ] 9. 实现界面动画和交互效果
  - [ ] 9.1 添加按钮交互动画
    - 实现按钮悬停缩放效果（1.05倍）
    - 添加按钮点击缩放效果（0.95倍）
    - 创建界面切换的淡入淡出动画（0.3秒）
    - 优化动画性能和流畅度
    - _需求: 6.5, 6.6_

  - [ ] 9.2 实现手势支持
    - 添加长按快速增减数值功能
    - 实现双击重置为默认值
    - 创建拖动调节滑块数值功能
    - 测试手势识别的准确性
    - _需求: 1.10, 6.8, 6.9_

- [ ] 10. 实现本地化支持
  - [ ] 10.1 创建本地化基础设施
    - 创建Localizable.strings文件（中英文）
    - 实现InfoPlist.strings本地化
    - 添加本地化字符串的统一管理
    - 创建本地化测试用例
    - _需求: 7.1, 7.2, 7.4_

  - [ ] 10.2 实现界面文字本地化
    - 本地化所有界面文字和按钮标签
    - 实现通知内容的本地化
    - 添加日期时间格式的本地化
    - 测试语言切换的界面适配
    - _需求: 7.2, 7.3, 7.4_

- [ ] 11. 实现数据持久化和配置管理
  - [ ] 11.1 创建设置数据管理
    - 实现UserDefaults的配置存储
    - 创建设置数据的编码/解码
    - 添加配置迁移和版本兼容性
    - 实现设置重置和默认值恢复
    - _需求: 1.1, 1.2, 1.3, 2.4, 2.5_

  - [ ] 11.2 实现应用状态持久化
    - 保存计时器配置和音效设置
    - 实现应用重启后状态恢复
    - 添加数据完整性验证
    - 创建配置导入导出功能（可选）
    - _需求: 8.1, 8.2, 8.3_

- [ ] 12. 实现错误处理和日志系统
  - [ ] 12.1 创建错误处理机制
    - 定义ZenTomatoError错误类型
    - 实现权限错误的友好提示界面
    - 添加音频错误的静默处理
    - 创建配置错误的自动恢复机制
    - _需求: 3.4, 2.6_

  - [ ] 12.2 实现日志系统
    - 创建Logger类和日志级别定义
    - 实现日志的格式化和输出
    - 添加调试模式的详细日志
    - 创建日志文件的管理和清理
    - _需求: 8.4_

- [ ] 13. 性能优化和测试
  - [ ] 13.1 实现性能监控
    - 添加内存使用监控
    - 实现CPU使用率检测
    - 创建启动时间测量
    - 添加响应时间统计
    - _需求: 8.1, 8.2, 8.3, 8.4, 8.5_

  - [ ] 13.2 创建单元测试套件
    - 编写TimerEngine的完整测试用例
    - 创建AudioPlayer功能测试
    - 实现NotificationManager测试
    - 添加数据模型和配置测试
    - _需求: 所有功能需求的验证_

  - [ ] 13.3 实现UI测试
    - 创建主要用户流程的UI测试
    - 测试菜单栏集成和弹出面板
    - 验证动画效果和交互响应
    - 添加多语言界面测试
    - _需求: 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 6.7, 6.8, 6.9_

- [ ] 14. 兼容性测试和优化
  - [ ] 14.1 实现系统兼容性测试
    - 测试macOS 11.0-14.x的兼容性
    - 验证Apple Silicon和Intel处理器支持
    - 测试多显示器环境下的表现
    - 验证Retina显示屏的高清显示
    - _需求: 8.6, 8.7, 8.8_

  - [ ] 14.2 实现长期稳定性测试
    - 进行24小时连续运行测试
    - 检测内存泄漏和资源释放
    - 验证计时精度的长期稳定性
    - 测试系统休眠唤醒后的状态恢复
    - _需求: 8.1, 8.2, 8.3, 8.4_

- [ ] 15. 应用打包和发布准备
  - [ ] 15.1 配置应用签名和权限
    - 配置Apple Developer证书签名
    - 设置Hardened Runtime和Entitlements
    - 实现应用沙盒兼容性
    - 创建应用图标和资源文件
    - _需求: 8.6, 8.7_

  - [ ] 15.2 准备App Store发布
    - 创建应用截图和描述
    - 编写隐私政策和使用条款
    - 实现应用元数据本地化
    - 进行最终的发布前测试
    - _需求: 7.1, 7.2, 8.6, 8.7_