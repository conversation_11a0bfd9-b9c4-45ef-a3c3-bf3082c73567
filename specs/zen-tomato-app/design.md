# 设计文档

## 概述

禅番茄（ZenTomato）采用现代化的SwiftUI + Combine架构，遵循MVVM设计模式，为macOS平台提供原生的番茄钟应用体验。应用以菜单栏为主要交互入口，通过弹出面板提供完整功能，同时深度集成macOS系统特性如通知中心、全局快捷键和开机自启动。

设计核心理念是"禅意极简"，通过优雅的视觉设计和流畅的交互体验，帮助用户在专注工作和放松休息之间找到平衡。

## 架构

### 整体架构

```mermaid
graph TB
    A[ZenTomatoApp] --> B[MenuBarManager]
    A --> C[TimerEngine]
    A --> D[AudioPlayer]
    A --> E[NotificationManager]
    A --> F[SettingsManager]
    
    B --> G[PopoverWindow]
    G --> H[MainView]
    H --> I[TimerView]
    H --> J[SettingsView]
    H --> K[AudioView]
    
    C --> L[TimerState]
    C --> M[TimerConfiguration]
    
    D --> N[AudioEngine]
    D --> O[SoundAssets]
    
    E --> P[UserNotifications]
    
    F --> Q[UserDefaults]
    F --> R[KeyboardShortcuts]
    F --> S[LaunchAtLogin]
```

### 技术栈

- **UI框架**: SwiftUI 4.0+
- **响应式编程**: Combine
- **音频处理**: AVFoundation
- **通知系统**: UserNotifications
- **数据持久化**: UserDefaults
- **全局快捷键**: KeyboardShortcuts (第三方库)
- **开机自启动**: LaunchAtLogin (第三方库)
- **最低系统要求**: macOS 11.0 (Big Sur)

### 架构模式

采用MVVM (Model-View-ViewModel) 架构模式：

- **Model**: 数据模型和业务逻辑 (TimerState, TimerConfiguration, AudioSettings)
- **View**: SwiftUI视图组件 (MainView, TimerView, SettingsView)
- **ViewModel**: 视图模型和状态管理 (TimerEngine, AudioPlayer, SettingsManager)

## 组件和接口

### 1. 核心计时引擎 (TimerEngine)

**职责**: 管理番茄钟的核心计时逻辑和状态转换

```swift
class TimerEngine: ObservableObject {
    @Published var currentState: TimerState
    @Published var timeRemaining: TimeInterval
    @Published var currentPhase: TimerPhase
    @Published var completedCycles: Int
    
    // 配置属性
    var workDuration: TimeInterval
    var shortBreakDuration: TimeInterval
    var longBreakDuration: TimeInterval
    var cyclesBeforeLongBreak: Int
    
    // 核心方法
    func start()
    func pause()
    func stop()
    func skip()
    func reset()
}
```

**关键特性**:
- 使用DispatchSourceTimer实现精确计时（误差<0.1秒）
- 支持暂停/继续功能
- 自动状态转换（工作→短休息→工作→长休息）
- 配置持久化到UserDefaults

### 2. 菜单栏管理器 (MenuBarManager)

**职责**: 管理菜单栏图标、状态显示和弹出面板

```swift
class MenuBarManager: NSObject, ObservableObject {
    private var statusItem: NSStatusItem?
    private var popover: NSPopover?
    
    @Published var showTimeInMenuBar: Bool
    @Published var isPopoverShown: Bool
    
    func setupMenuBar()
    func updateMenuBarTitle(_ text: String)
    func showPopover()
    func hidePopover()
    func togglePopover()
}
```

**关键特性**:
- 自定义NSStatusItem显示番茄图标
- 可选显示倒计时（MM:SS格式）
- 弹出面板尺寸：380x680像素
- 智能定位避免超出屏幕边界
- 点击外部自动关闭

### 3. 音频播放器 (AudioPlayer)

**职责**: 管理所有音效的播放和音量控制

```swift
class AudioPlayer: ObservableObject {
    @Published var windupVolume: Float = 1.0
    @Published var dingVolume: Float = 1.0
    @Published var tickingVolume: Float = 0.5
    @Published var isMuted: Bool = false
    
    private var audioEngine: AVAudioEngine
    private var players: [String: AVAudioPlayer]
    
    func playWindupSound()
    func playDingSound()
    func startTickingSound()
    func stopTickingSound()
    func setVolume(for sound: SoundType, volume: Float)
}
```

**关键特性**:
- 支持三种音效：开始音(windup)、结束音(ding)、背景音(ticking)
- 独立音量控制（0-2倍范围）
- 音量渐变效果（0.1秒淡入淡出）
- 高品质WAV格式音频文件

### 4. 通知管理器 (NotificationManager)

**职责**: 处理系统通知的发送和权限管理

```swift
class NotificationManager: NSObject, ObservableObject {
    @Published var isAuthorized: Bool = false
    
    func requestPermission()
    func sendBreakStartNotification(duration: TimeInterval)
    func sendBreakEndNotification()
    func sendWorkStartNotification()
    func handleNotificationResponse(_ response: UNNotificationResponse)
}
```

**关键特性**:
- 集成UserNotifications框架
- 支持交互式通知（跳过休息）
- 权限状态监控和引导
- 本地化通知内容

### 5. 设置管理器 (SettingsManager)

**职责**: 管理应用设置和系统集成功能

```swift
class SettingsManager: ObservableObject {
    @Published var globalShortcut: KeyboardShortcuts.Shortcut?
    @Published var launchAtLogin: Bool = false
    @Published var showTimeInMenuBar: Bool = true
    
    func setupGlobalShortcut()
    func toggleLaunchAtLogin()
    func handleURLScheme(_ url: URL)
    func resetToDefaults()
}
```

**关键特性**:
- 全局快捷键支持（使用KeyboardShortcuts库）
- 开机自启动（使用LaunchAtLogin库）
- URL Scheme处理（zentomato://）
- 设置数据持久化

## 数据模型

### TimerState 枚举

```swift
enum TimerState: String, CaseIterable {
    case idle = "idle"
    case running = "running"
    case paused = "paused"
    case completed = "completed"
}
```

### TimerPhase 枚举

```swift
enum TimerPhase: String, CaseIterable {
    case work = "work"
    case shortBreak = "shortBreak"
    case longBreak = "longBreak"
    
    var displayName: String { ... }
    var color: Color { ... }
}
```

### TimerConfiguration 结构体

```swift
struct TimerConfiguration: Codable {
    var workDuration: TimeInterval = 25 * 60
    var shortBreakDuration: TimeInterval = 5 * 60
    var longBreakDuration: TimeInterval = 15 * 60
    var cyclesBeforeLongBreak: Int = 4
    var autoStartBreaks: Bool = false
    var autoStartWork: Bool = false
}
```

### AudioSettings 结构体

```swift
struct AudioSettings: Codable {
    var windupVolume: Float = 1.0
    var dingVolume: Float = 1.0
    var tickingVolume: Float = 0.5
    var isMuted: Bool = false
    var enableTicking: Bool = true
}
```

## 用户界面设计

### 设计系统

**颜色规范**:
```swift
extension Color {
    static let zenRed = Color(hex: "#CC3333")      // 工作状态
    static let zenGreen = Color(hex: "#66B366")    // 休息状态
    static let zenBlue = Color(hex: "#6699CC")     // 专注状态
    static let zenGold = Color(hex: "#E6B34D")     // 完成状态
    static let zenGray = Color(hex: "#F2F2F2")     // 背景色
    static let zenTextGray = Color(hex: "#4D4D4D") // 文字色
}
```

**动画规范**:
```swift
extension Animation {
    static let zenQuick = Animation.easeInOut(duration: 0.2)
    static let zenSmooth = Animation.easeInOut(duration: 0.3)
    static let zenSlow = Animation.easeInOut(duration: 0.8)
    static let zenBreathing = Animation.easeInOut(duration: 2.0).repeatForever(autoreverses: true)
}
```

### 主要视图组件

#### 1. MainView - 主控制面板

```swift
struct MainView: View {
    @StateObject private var timerEngine = TimerEngine()
    @StateObject private var audioPlayer = AudioPlayer()
    @State private var selectedTab: TabType = .timer
    
    var body: some View {
        VStack(spacing: 0) {
            // 呼吸动画背景区域 (80px)
            BreathingBackgroundView()
            
            // 标签页切换 (60px)
            TabSelectorView(selection: $selectedTab)
            
            // 内容区域 (350px)
            TabContentView(selectedTab: selectedTab)
            
            // 底部操作栏 (60px)
            BottomActionBar()
        }
        .frame(width: 380, height: 680)
        .background(Color.zenGray)
        .cornerRadius(16)
        .shadow(radius: 8)
    }
}
```

#### 2. TimerView - 计时器视图

```swift
struct TimerView: View {
    @ObservedObject var timerEngine: TimerEngine
    
    var body: some View {
        VStack(spacing: 24) {
            // 大型时间显示
            TimeDisplayView(timeRemaining: timerEngine.timeRemaining)
            
            // 阶段指示器
            PhaseIndicatorView(currentPhase: timerEngine.currentPhase)
            
            // 主控制按钮
            MainControlButton(
                state: timerEngine.currentState,
                action: { timerEngine.toggleTimer() }
            )
            
            // 时间设置控件
            TimeSettingsView(configuration: $timerEngine.configuration)
        }
        .padding()
    }
}
```

#### 3. SettingsView - 设置视图

```swift
struct SettingsView: View {
    @ObservedObject var settingsManager: SettingsManager
    
    var body: some View {
        VStack(spacing: 16) {
            // 全局快捷键设置
            GlobalShortcutView(shortcut: $settingsManager.globalShortcut)
            
            // 开机自启动
            Toggle("开机自启动", isOn: $settingsManager.launchAtLogin)
            
            // 菜单栏显示选项
            Toggle("在菜单栏显示时间", isOn: $settingsManager.showTimeInMenuBar)
            
            // 重置按钮
            Button("重置为默认设置") {
                settingsManager.resetToDefaults()
            }
        }
        .padding()
    }
}
```

#### 4. AudioView - 音效设置视图

```swift
struct AudioView: View {
    @ObservedObject var audioPlayer: AudioPlayer
    
    var body: some View {
        VStack(spacing: 20) {
            // 音效开关
            Toggle("启用音效", isOn: $audioPlayer.isEnabled)
            
            // 各种音效的音量控制
            VolumeSliderView(
                title: "开始音效",
                volume: $audioPlayer.windupVolume,
                onTest: { audioPlayer.playWindupSound() }
            )
            
            VolumeSliderView(
                title: "结束音效", 
                volume: $audioPlayer.dingVolume,
                onTest: { audioPlayer.playDingSound() }
            )
            
            VolumeSliderView(
                title: "背景滴答声",
                volume: $audioPlayer.tickingVolume,
                onTest: { audioPlayer.toggleTicking() }
            )
        }
        .padding()
    }
}
```

### 自定义组件

#### BreathingBackgroundView - 呼吸动画背景

```swift
struct BreathingBackgroundView: View {
    @State private var isBreathing = false
    
    var body: some View {
        ZStack {
            Circle()
                .fill(Color.zenRed.opacity(0.1))
                .scaleEffect(isBreathing ? 1.2 : 1.0)
                .animation(.zenBreathing, value: isBreathing)
        }
        .frame(height: 80)
        .onAppear {
            isBreathing = true
        }
    }
}
```

#### VolumeSliderView - 音量滑块组件

```swift
struct VolumeSliderView: View {
    let title: String
    @Binding var volume: Float
    let onTest: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                Spacer()
                Button("测试", action: onTest)
                    .buttonStyle(.borderless)
            }
            
            HStack {
                Slider(value: $volume, in: 0...2)
                    .onTapGesture(count: 2) {
                        volume = 1.0 // 双击重置
                    }
                
                Text("\(Int(volume * 100))%")
                    .frame(width: 40)
                    .foregroundColor(.secondary)
            }
        }
    }
}
```

## 错误处理

### 错误类型定义

```swift
enum ZenTomatoError: LocalizedError {
    case audioPermissionDenied
    case notificationPermissionDenied
    case audioFileNotFound(String)
    case timerAlreadyRunning
    case invalidConfiguration
    
    var errorDescription: String? {
        switch self {
        case .audioPermissionDenied:
            return "需要音频权限来播放提示音"
        case .notificationPermissionDenied:
            return "需要通知权限来发送提醒"
        case .audioFileNotFound(let filename):
            return "找不到音频文件: \(filename)"
        case .timerAlreadyRunning:
            return "计时器已在运行中"
        case .invalidConfiguration:
            return "无效的计时器配置"
        }
    }
}
```

### 错误处理策略

1. **权限错误**: 显示友好的权限请求界面，提供系统设置快捷入口
2. **音频错误**: 静默处理，记录日志，不影响核心计时功能
3. **配置错误**: 自动重置为默认配置，显示提示信息
4. **网络错误**: 本应用为离线应用，无网络依赖

### 日志系统

```swift
class Logger {
    static let shared = Logger()
    
    func log(_ message: String, level: LogLevel = .info) {
        let timestamp = DateFormatter.iso8601.string(from: Date())
        let logEntry = "[\(timestamp)] [\(level.rawValue)] \(message)"
        
        #if DEBUG
        print(logEntry)
        #endif
        
        // 写入日志文件（可选）
        writeToLogFile(logEntry)
    }
}
```

## 测试策略

### 单元测试

**TimerEngine测试**:
```swift
class TimerEngineTests: XCTestCase {
    func testTimerStartStop() {
        let engine = TimerEngine()
        engine.start()
        XCTAssertEqual(engine.currentState, .running)
        
        engine.stop()
        XCTAssertEqual(engine.currentState, .idle)
    }
    
    func testPhaseTransition() {
        let engine = TimerEngine()
        engine.workDuration = 1 // 1秒用于测试
        engine.start()
        
        let expectation = XCTestExpectation(description: "Phase transition")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.1) {
            XCTAssertEqual(engine.currentPhase, .shortBreak)
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 2.0)
    }
}
```

**AudioPlayer测试**:
```swift
class AudioPlayerTests: XCTestCase {
    func testVolumeControl() {
        let player = AudioPlayer()
        player.setVolume(for: .windup, volume: 0.5)
        XCTAssertEqual(player.windupVolume, 0.5)
    }
    
    func testMuteFunction() {
        let player = AudioPlayer()
        player.isMuted = true
        player.playWindupSound()
        // 验证静音时不播放声音
    }
}
```

### 集成测试

**菜单栏集成测试**:
- 验证菜单栏图标正确显示
- 验证弹出面板正确定位
- 验证点击外部关闭功能

**通知系统测试**:
- 验证通知权限请求
- 验证通知内容正确性
- 验证交互式通知响应

### UI测试

**主要用户流程测试**:
```swift
class ZenTomatoUITests: XCTestCase {
    func testCompleteWorkCycle() {
        let app = XCUIApplication()
        app.launch()
        
        // 点击开始按钮
        app.buttons["开始"].tap()
        
        // 验证计时器开始运行
        XCTAssertTrue(app.staticTexts.matching(identifier: "timer-display").element.exists)
        
        // 等待工作周期完成（使用较短的测试时间）
        // 验证自动切换到休息状态
    }
}
```

### 性能测试

**内存泄漏测试**:
- 使用Instruments检测内存泄漏
- 长时间运行测试（24小时）
- 验证内存占用稳定在50MB以下

**CPU使用率测试**:
- 空闲状态CPU占用 < 1%
- 活动状态CPU占用 < 5%
- 动画播放时的性能影响

## 部署和分发

### 构建配置

**Release配置**:
```swift
// Build Settings
SWIFT_COMPILATION_MODE = wholemodule
SWIFT_OPTIMIZATION_LEVEL = -O
GCC_OPTIMIZATION_LEVEL = s
ENABLE_BITCODE = NO (macOS不需要)
```

**代码签名**:
- 使用Apple Developer证书签名
- 启用Hardened Runtime
- 配置Entitlements文件

### App Store准备

**元数据**:
- 应用名称: 禅番茄 ZenTomato
- 分类: 效率工具
- 关键词: 番茄钟, 专注, 时间管理, 效率
- 隐私政策: 本地数据存储，无数据收集

**截图要求**:
- macOS应用截图 (1280x800, 2560x1600)
- 展示主要功能界面
- 突出禅意美学设计

**审核准备**:
- 遵循App Store Review Guidelines
- 提供测试账号（如需要）
- 准备审核说明文档

### 本地化

**支持语言**:
- 简体中文 (zh-Hans) - 主要语言
- 英文 (en) - 国际版本

**本地化文件结构**:
```
Resources/
├── zh-Hans.lproj/
│   ├── Localizable.strings
│   └── InfoPlist.strings
└── en.lproj/
    ├── Localizable.strings
    └── InfoPlist.strings
```

这个设计文档为ZenTomato应用提供了完整的技术架构和实现指导，确保应用能够满足所有功能需求，同时保持高质量的用户体验和系统性能。