# 通知系统调试指南

## 🔍 问题诊断

您反馈没有收到系统通知，我已经进行了以下修复和增强：

### ✅ 已修复的问题

1. **✅ 添加了沙盒通知权限** - 在 `ZenTomato.entitlements` 中添加了 `com.apple.security.usernotifications.communication`
2. **✅ 增强了调试日志** - 添加了详细的权限状态和发送状态日志
3. **✅ 添加了测试通知功能** - 在设置界面中添加了"发送测试通知"按钮
4. **✅ 改进了权限检查** - 实时检查权限状态并提供详细反馈

## 🧪 测试步骤

### 第一步：检查权限状态
1. 运行应用
2. 打开设置界面（点击菜单栏图标 → 设置）
3. 查看"通知设置"部分的权限状态

### 第二步：测试通知功能
1. 如果权限显示"已授权"，点击"发送测试通知"按钮
2. 检查控制台日志输出（在 Xcode 中查看）
3. 观察是否收到测试通知

### 第三步：检查系统设置
如果仍然没有收到通知，请检查：
1. **系统偏好设置** → **通知与专注模式**
2. 找到 **ZenTomato** 应用
3. 确保以下设置已启用：
   - ✅ 允许通知
   - ✅ 横幅
   - ✅ 声音
   - ✅ 在通知中心显示

## 📋 调试日志说明

运行应用时，您应该在控制台看到以下日志：

### 应用启动时：
```
🔔 开始请求通知权限...
✅ 通知权限请求结果: 已授权
📱 通知权限状态: 已授权
📱 通知设置详情:
   - 授权状态: authorized
   - 横幅设置: enabled
   - 声音设置: enabled
   - 角标设置: enabled
   - 通知中心设置: enabled
   - 锁屏设置: enabled
```

### 发送通知时：
```
🔔 尝试发送通知: test-1692123456.789
   - 标题: ZenTomato 测试通知
   - 内容: 如果您看到这条通知，说明通知系统工作正常！
   - 权限状态: 已授权
📤 发送通知请求...
✅ 通知发送成功: test-1692123456.789
```

### 如果权限被拒绝：
```
❌ 通知未授权，无法发送通知
💡 请在系统偏好设置 > 通知与专注模式中启用 ZenTomato 的通知权限
```

## 🔧 可能的问题和解决方案

### 问题1：权限显示"未授权"
**解决方案：**
1. 点击"请求权限"按钮
2. 在弹出的系统对话框中选择"允许"
3. 如果没有弹出对话框，手动到系统设置中启用

### 问题2：权限显示"已授权"但没有通知
**解决方案：**
1. 检查"勿扰模式"是否开启
2. 检查应用是否在系统通知设置中被禁用
3. 重启应用并重新测试

### 问题3：通知发送成功但看不到
**可能原因：**
1. 应用在前台时，通知可能不显示横幅（这是正常的）
2. 通知被系统的"勿扰模式"或"专注模式"阻止
3. 通知设置中的"横幅样式"设置为"无"

**解决方案：**
1. 将应用最小化到后台再测试
2. 检查通知中心是否有通知记录
3. 调整系统通知设置

## 🎯 验证通知功能的完整流程

1. **启动应用** → 检查权限请求日志
2. **打开设置** → 查看权限状态
3. **点击测试按钮** → 观察日志和通知
4. **开始工作计时** → 完成后检查是否收到休息通知
5. **在休息期间** → 测试快速操作按钮

## 📞 如果问题仍然存在

如果按照以上步骤仍然无法收到通知，请提供：
1. 控制台的完整日志输出
2. 系统通知设置的截图
3. 应用权限状态的截图

这将帮助我进一步诊断问题。

---

**重要提示：** 
- macOS 的通知系统有时需要重启应用或系统才能生效
- 确保应用有正确的代码签名和权限配置
- 沙盒应用的通知权限可能需要特殊处理
